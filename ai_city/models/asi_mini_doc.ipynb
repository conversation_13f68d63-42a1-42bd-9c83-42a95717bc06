{"cells": [{"cell_type": "markdown", "id": "caba1d7b", "metadata": {}, "source": ["#### Test sending text from pdfs to Asi One to find if the pacient has cancer "]}, {"cell_type": "code", "execution_count": null, "id": "af0f5b03", "metadata": {}, "outputs": [], "source": ["from utils.utils import extract_text_from_pdf\n", "from asi_mini import call_asi_one_chatbot"]}, {"cell_type": "code", "execution_count": null, "id": "8df90444", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Laborator de anatomie patologica - str<PERSON>, 4\n", "Cluj-Napoca\n", "Tel: ********** \n", "SC Santomar Oncodiagnostic SRL\n", "Buletin de analize medicale\n", "Nume:\n", "Adresa: \n", "Unitate recoltare: ART ESTET Clinica\n", "Telefon: **********\n", "<PERSON><PERSON>\n", "Trimitator: <PERSON>\n", "CNP: 2880727261705\n", "Varsta:  33 ani, 10 luni\n", "Sex: F\n", "Cod pacient: 203075075\n", "Data - ora cerere: 27.05.2022 - 15:15\n", "Proba recoltata extern \n", "Id proba:\n", "202229523\n", " <PERSON><PERSON><PERSON>                                                                          \n", "<PERSON><PERSON><PERSON> in afara limitelor admise pentru varsta si sexul respectiv sunt in chenar\n", "*202229523*\n", "UM\n", "Rezultate\n", "Recomandare generala: Rezultatele analizelor trebuie interpretate de catre medicul dumneavoastra curant in context clinic.\n", "Probele analizate: Toate probele procesate cu rezultate finale si fara comentarii aditionale sunt considerate conforme . \n", "Interval biologic de \n", "<PERSON><PERSON>a\n", "Cod proba:\n", "312326352\n", "HISTOPATOLOGIE\n", "EXAMEN HISTOPATOLOGIC\n", "   <PERSON><PERSON><PERSON> inregistrare proba laborator:\n", "2022 29523\n", "   Diagnostic clinic:\n", "Formatiune tumorala san drept, cadran inferior extern- fibroadenom\n", "   Specimen analizat:\n", "excizie\n", "   Descriere macroscopica:\n", "Piesa de 4,5/2,5/2,5 cm cu formatiune nodulara alb-gri de 2,5/2,3 cm cu mic chist de 0,5/0,2 cm\n", "   Descriere microscopica:\n", " Materialul trimis prezinta structura unui fibroadenom peri- si intracanalicular, cu redusa hiperplazie epiteliala \n", "cribriforma tipica, rare ducte cu metaplazie apocrina micropapilara sau mici chisturi mamare. Redus infiltrat \n", "limfocitar adiacent unor lobuli restanti.\n", " Stroma este fibroasa hipocelulara, cu vase capilare fine.\n", "   Diagnostic histopatologic:\n", "Aspectul histologic apartine unui fibroadenom mamar.\n", "Validat:\n", "Nota: Copia acestui rezultat are scopul unei informari operative dar nu se poate substitui documentului original semnat si parafat de catre medic.\n", "¹ examinari efectuate in laborator partener.\n", "Pentru probele recoltate extern, esantioanele din materii fecale, esantioanele de urina si esantioane autorecoltate, intreaga responsabilitate privind \n", "provenienta, apartenenta, modul de recoltare, depozitare si transport apartine in exclusivitate solicitantului, acestea putand impacta rezultatul final al \n", "testarii si interpretarea lui.\n", "Datele dumneavoastra personale sunt prelucrate de Regina Maria, in scopul prestarii de servicii medicale. Va puteti exercita drepturile prevazute \n", "in Regulamentul nr. 679 din 27 aprilie 2016 al Parlamentului European și al Consiliului Uniunii Europene in conditiile mentionate in Nota de informare cu \n", "privire la prelucrarea datelor cu caracter personal, disponibila pe site-ul “www.reginamaria.ro”, sectiunea “GDPR”, \n", "https://www.reginamaria.ro/sites/default/files/nota_informare.pdf.  \n", "Pentru vizualizarea rezultatelor analizelor si pentru a avea acces oricand la istoricul acestora, va sugeram sa accesati “Contul meu” disponibil pe \n", "prima pagina a site-ului retelei, “https://contulmeu.reginamaria.ro”\n", "█▀▀▀▀▀█  █  ▀▄██▄▄ ▀▀▄▄▀▀  ▄▀▀▀█▀  ▄▀▄▄▄█ █▀▀▀▀▀█\n", "█ ███ █ █ ██ ▀ ▄▄▄ █ ▀▄▀█▄ ▄▀▀ ▄▀▄ ▄▄▄ █▀ █ ███ █\n", "█ ▀▀▀ █ █▀▄█▄▄ █▄█▄▄ ██▀▀▀█▄ █▀▄ ▄▄▀▄█▄   █ ▀▀▀ █\n", "▀▀▀▀▀▀▀ █ █▄▀ █ █▄█ ▀ █ ▀ █▄█ █▄█ █ ▀▄▀ ▀ ▀▀▀▀▀▀▀\n", "█ ████▀▄▄▄▀██▄▀ ▀ █▄▀▀▀███▀▄▄▄▄█▄▀ ███ ▄▀ █▀█▀▀▄ \n", "█▀▀▄▄ ▀  ▀██▄█▀  ▄▄ ▀▄ ▄▀▄▀▄██▄▀███▄ ▄█ ▀▀ ▀▄▀  █\n", "▄█▀  ▀▄▀▄▄█▀▄ ▀▀  ▄▄ █▀▀▄ ▄▄▀█ ▄▀ ██▄ ▀▀ ▄█ ▀ █▀\n", "▄ █ ▄▀▀▄██ █▀█▄▄▄ █▄█▀██▀ █ ██ ▄▄▄█ ▀▄▀▀  ▄▀▄█ ▀█\n", "▄▄   █▀ █▀▀█▀▄▀██▀ ▄ █ ▀▀  █  █ ▄▀ ▀██  ▀▄▄█  ▀▄ \n", "▄  ▄  ▀▄ ▀█ █▄▀██   ▄▄  ▀██▀▄▀▄ ▄  █▀▀█▄▀▀▀█▄▀  █\n", "█  ▀▄▄▀██▀ █   █▀▄█▄▀▀█▀▀▄█▀▄▄▄█▄▄ ██▄▀▀▀▄█▄▄ ▀▄▀\n", "▀▄▀█▀▀▀█▄  ▀█▀█▀██   █▀▀▀█▄▄▄▄▀█▀▀ ▀██▀█▀▀▀██ ▀▀\n", "▀▀▀▀█ ▀ █ ██▀█▀▀ ██▀▀▀█ ▀ █   █▀   ▀▀▄ ██ ▀ █▀▀▄▀\n", "██▀▄█▀▀█▀▀ ▀▄▄█ ▄▄███▄█▀█▀▀█▄▄  ▄▄▄▀▀█▀ ▀▀▀▀▀▀▀ █\n", "▄ ▄███▀█▀  ▄██▀  ▄█▀ ██▄▀█▀  ███ █ ▀██ ▀███▀▀▀▀▀▀\n", "▄█ ▄██▀█ █▄▀██▀▄   ▀ ▄█▄ ▀▄▀█ ▄▄▄▄▀█▀▀▀▀▄▄▀█▀ ▀▀▀\n", "█ ▄ ▀▄▀█▄ ▄█▀█▄█▀ ▀▀▀█▄▀▄▀▀▀▄██  █ ▄▀▄▀▀▄█ ▄▀ ▀█ \n", "█▄▀▀ ▀▀ ▄▄█▀▀█▀▄█  ▄▄▀▄▀▄▀▄ ▄▄▄▀▄ █▀ ▄  █ ▀▄▄▀  ▀\n", "█▄█▀█▀▀█▀ ▄▄▄▀▀ ▀▀▄█▀▄▀█▄▄▄█ ███▄  ▀▀█ ██ ▀▄▀ ██ \n", "█▄▄ ▀▀▄█▀ ▀  ▄▀▄▀▄█ ▄███▄▄█▄▄ ▄▄█▀▀▄▄▀▀█▀▀█▀ ▀█▀\n", "▀▀▀   ▀▀▄ ▄█▄▀▀ ▄▀▀▄▀▀█▀▀▀█▄ █▄█ ▀▀█▀▄ ▀█▀▀▀█▄▄█ \n", "█▀▀▀▀▀█ ▄▀▀██▄ ██▀  ▀██ ▀ ███▄█▀▄▀█▄▀▀▀ █ ▀ █   ▀\n", "█ ███ █ ██▀ ▀   ▀ ▄▄▄▄██▀▀▀ ▄ ▄▀  ▀▀█ ▀▄▀▀████▀ ▄\n", "█ ▀▀▀ █ ▀▄  ▄ ▀▄ ▀▀▄██ ██▀  ▄█ ▀▀▀▀▀ ▄▀▄▄██▀ ▄  ▄\n", "▀▀▀▀▀▀▀ ▀▀  ▀  ▀  ▀     ▀▀ ▀   ▀   ▀▀         ▀▀▀\n", "<PERSON><PERSON><PERSON> pen<PERSON>u \n", "varianta digitala\n", "Pagina 1 din 1\n", "Eliberat la  03.06.2022 17:25\n", "FG-5.8-01, versiunea din 1.10.2021\n", "\n"]}], "source": ["pdf_path = \"../data/result_1.pdf\"\n", "pdf_text = extract_text_from_pdf(pdf_path)\n", "print(pdf_text)"]}, {"cell_type": "code", "execution_count": null, "id": "47d61a2b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}