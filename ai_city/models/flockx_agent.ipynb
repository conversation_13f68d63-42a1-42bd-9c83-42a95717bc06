{"cells": [{"cell_type": "markdown", "id": "fdf6e297", "metadata": {}, "source": ["### Flockx API not available >:("]}, {"cell_type": "code", "execution_count": 2, "id": "91277bc5", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "21614f14", "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "\n", "FLOCKX_KEY = os.getenv('FLOCKX_KEY')\n", "AGENT_ID = \"af1d4156-2b7b-45cc-98d2-ee8f96652c56\""]}, {"cell_type": "code", "execution_count": null, "id": "c7140a96", "metadata": {}, "outputs": [], "source": ["def call_flockx_agent(user_input):\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'Token {FLOCKX_KEY}'\n", "    }\n", "\n", "    payload = json.dumps({\n", "        \"agent_id\": AGENT_ID,\n", "        \"input\": user_input\n", "    })\n", "\n", "    url = f\"https://api.flockx.io/api/v1/agents/{AGENT_ID}/prompt\"\n", "\n", "    response = requests.post(url, headers=headers, data=payload)\n", "\n", "    if response.status_code == 200:\n", "        return response.json().get(\"response\", \"No response\")\n", "    else:\n", "        return f\"Error: {response.status_code}, {response.text}\""]}, {"cell_type": "code", "execution_count": 9, "id": "6376aedf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: 401, {\"detail\":\"<PERSON><PERSON> or <PERSON> authorization required\"}\n"]}], "source": ["print(call_flockx_agent(\"I want to know if I have breast cancer\"))"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}