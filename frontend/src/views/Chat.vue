<template>
  <div class="flex h-[calc(100vh-5rem)] flex-col w-full lg:max-w-2xl m-auto">
    <div ref="messagesContainer" class="flex-1 overflow-y-auto w-full">
      <Message
        v-for="(message, index) in messages"
        :key="`${message.timestamp}-${index}`"
        :role="message.role"
        :content="message.content"
        :is-last-message="index === messages.length - 1"
        :timestamp="message.timestamp"
      />
    </div>
    <form @submit="onSubmit" class="flex flex-row w-full px-2 py-2 border-t bg-background">
      <Input
        type="text"
        :placeholder="t('ENTER_YOUR_MESSAGE')"
        v-model="newMessage"
        class="flex-1 mr-2"
      />
      <Button type="submit" :disabled="!newMessage.trim()">
        <SendHorizontal class="h-4 w-4"/>
      </Button>
    </form>
  </div>
</template>

<script setup lang="ts">
import {type Ref, ref, nextTick, watch, onMounted, onBeforeMount} from 'vue'
import {Message} from '@/components/ui/message'
import {Input} from '@/components/ui/input'
import {useI18n} from 'vue-i18n'
import {Button} from '@/components/ui/button'
import {SendHorizontal} from 'lucide-vue-next'
import axios from "axios";

const {t} = useI18n()

const newMessage = ref('')
const messagesContainer = ref<HTMLElement>()

const messages: Ref<{ role: 'user' | 'assistant'; content: string; timestamp: number }[]> = ref([])
const conversation = ref();

const getExistingConversation = async () => {
  const result = await axios.get('/conversations/my-conversations')
  if (Array.isArray(result.data)) {
    conversation.value = result.data[0] // Hardcoded for now as we do not support more than one conversation
  }
}

// Check if user is at bottom of scroll
const isAtBottom = (): boolean => {
  if (!messagesContainer.value) return true
  const {scrollTop, scrollHeight, clientHeight} = messagesContainer.value
  return scrollTop + clientHeight >= scrollHeight - 10 // 10px threshold
}

// Scroll to bottom
const scrollToBottom = () => {
  if (!messagesContainer.value) return
  messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
}

// Watch for new messages and auto-scroll if at bottom
watch(
  messages,
  async () => {
    const wasAtBottom = isAtBottom()
    await nextTick()
    if (wasAtBottom) {
      scrollToBottom()
    }
  },
  {deep: true},
)

const onSubmit = (e: Event) => {
  e.preventDefault()
  if (!newMessage.value.trim()) return

  sendMessage(newMessage.value)

  newMessage.value = ''
}

onBeforeMount(async () => {
  await getExistingConversation();
  if (!conversation.value) {
    const newConversation = await axios.post('/conversations', {})
    conversation.value = newConversation.data
  }

  messages.value = conversation.value.messages.map((message: any) => ({
    role: message.patient_id ? 'user' : 'assistant',
    content: message.content,
    timestamp: message.timestamp,
  }))
})

const sendMessage = async (content: string) => {
  messages.value.push({
    role: 'user',
    content,
    timestamp: (new Date()).getTime(),
  })

  const response = await axios.post(`/conversations/${conversation.value.id}/messages`, {
    content,
    message_type: 'text',
  })
  console.log(response.data);

  messages.value.push({
    role: 'assistant',
    content: response.data.content,
    timestamp: (new Date()).getTime(),
  })
}
</script>

<style scoped></style>
